"""
Modern Music module for Discord bot using Wavelink.

This module provides a complete music bot implementation using Wavelink
for reliable audio streaming without requiring local FFmpeg installation.
Features include YouTube, Spotify, and SoundCloud support with queue management,
volume control, and various playback controls.
"""

import asyncio
import discord
import wavelink
from discord.ext import commands
import random

try:
    from utilities import checks, decorators, converters
except ImportError:
    checks = decorators = converters = None

try:
    from settings import constants
except ImportError:
    constants = None


class MusicControlView(discord.ui.View):
    def __init__(self, player, ctx):
        super().__init__(timeout=None)
        self.player = player
        self.ctx = ctx

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if not self.ctx.voice_client or not self.player.playing:
            await interaction.response.send_message("I'm not currently playing music.", ephemeral=True)
            return False
        if interaction.user in self.ctx.voice_client.channel.members:
            return True
        await interaction.response.send_message(
            "Only members in the same voice channel can control the player.", ephemeral=True
        )
        return False

    @discord.ui.button(emoji="⏮️", style=discord.ButtonStyle.secondary)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_message("Previous track feature not implemented yet.", ephemeral=True)

    @discord.ui.button(emoji="<:o_pause:1302172935951351821>", style=discord.ButtonStyle.success)
    async def pause_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player.paused:
            await self.player.pause(False)
            # Update voice channel status to show playing
            await self.player.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{self.player.current.title}** - {self.player.current.author}")
            button.emoji = "<:o_pause:1302172935951351821>"
            await interaction.response.edit_message(view=self)
        elif self.player.playing:
            await self.player.pause(True)
            # Update voice channel status to show paused
            await self.player.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{self.player.current.title}** - {self.player.current.author}")
            button.emoji = "<:o_resume:1302173145980997712>"
            await interaction.response.edit_message(view=self)

    @discord.ui.button(emoji="⏭️", style=discord.ButtonStyle.secondary)
    async def skip_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player and self.player.playing:
            await self.player.stop()
            await interaction.response.send_message(f"Skipped by **{interaction.user.display_name}**.")
        else:
            await interaction.response.send_message("No song to skip.", ephemeral=True)

    @discord.ui.button(emoji="🔁", style=discord.ButtonStyle.secondary)
    async def loop_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.player.queue.mode = wavelink.QueueMode.loop if self.player.queue.mode != wavelink.QueueMode.loop else wavelink.QueueMode.normal
        await interaction.response.send_message(f"Loop {'enabled' if self.player.queue.mode == wavelink.QueueMode.loop else 'disabled'}.")

    @discord.ui.button(emoji="🔀", style=discord.ButtonStyle.secondary)
    async def shuffle_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player.queue:
            random.shuffle(self.player.queue)
            await interaction.response.send_message(f"Queue shuffled by **{interaction.user.display_name}**.")
        else:
            await interaction.response.send_message("Queue is empty.", ephemeral=True)

    @discord.ui.button(emoji="⏹️", style=discord.ButtonStyle.danger)
    async def stop_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player:
            # Clear voice channel status before disconnecting
            try:
                await self.player.channel.edit(status=None)
            except:
                pass  # Ignore errors if channel doesn't exist or no permissions
            await self.player.disconnect()
            await interaction.response.send_message(f"Stopped and disconnected by **{interaction.user.display_name}**.")
        else:
            await interaction.response.send_message("Not connected.", ephemeral=True)


class Music(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.bot.loop.create_task(self.connect_nodes())

    async def connect_nodes(self) -> None:
        """Connect to Lavalink nodes using Wavelink v3+"""
        await wavelink.Pool.connect(
            client=self.bot,
            nodes=[
                wavelink.Node(
                    uri="https://lava-v4.ajieblogs.eu.org:443",  # Replace if not HTTPS
                    password="https://dsc.gg/ajidevserver"           # Replace with actual password
                )
            ]
        )

    async def display_player_embed(self, player, track, ctx):
        duration = f"{track.length // 60000}:{(track.length // 1000) % 60:02d}"

        embed = discord.Embed(
            title=f"🎵 Now Playing",
            description=f"**[{track.title}]({track.uri})**",
            color=0x1DB954
        )
        embed.add_field(name="Artist", value=track.author, inline=True)
        embed.add_field(name="Duration", value=duration, inline=True)
        embed.add_field(name="Source", value="YouTube", inline=True)

        if track.artwork:
            embed.set_thumbnail(url=track.artwork)

        embed.set_footer(
            text=f"Requested by {ctx.author.display_name}",
            icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url
        )

        await ctx.send(embed=embed, view=MusicControlView(player, ctx))

    @commands.Cog.listener()
    async def on_wavelink_track_start(self, payload: wavelink.TrackStartEventPayload):
        player = payload.player
        track = player.current
        print(f"[MUSIC] Started playing: {track.title}")

        # Update voice channel status when track starts
        try:
            await player.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{track.title}** - {track.author}")
        except:
            pass  # Ignore errors if channel doesn't exist or no permissions

    @commands.Cog.listener()
    async def on_wavelink_track_end(self, payload: wavelink.TrackEndEventPayload):
        player = payload.player

        if not player.queue.is_empty:
            next_track = await player.queue.get_wait()
            await player.play(next_track)
            if hasattr(player, 'ctx'):
                await self.display_player_embed(player, next_track, player.ctx)
        else:
            await asyncio.sleep(30)
            if player.queue.is_empty and not player.playing:
                # Clear voice channel status before disconnecting
                try:
                    await player.channel.edit(status=None)
                except:
                    pass  # Ignore errors if channel doesn't exist or no permissions
                await player.disconnect()
                if hasattr(player, 'ctx'):
                    embed = discord.Embed(
                        description="Queue ended. Disconnected from voice channel.",
                        color=0xFF0000
                    )
                    await player.ctx.send(embed=embed)

    @commands.command(name="play", aliases=["p"], brief="Play a song from YouTube.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def play(self, ctx, *, query: str):
        if not ctx.author.voice:
            await ctx.send(embed=discord.Embed(description="❌ You need to be in a voice channel.", color=0xFF0000))
            return

        voice_channel = ctx.author.voice.channel
        perms = voice_channel.permissions_for(ctx.guild.me)

        if not perms.connect or not perms.speak:
            await ctx.send(embed=discord.Embed(description="❌ I need connect & speak permissions.", color=0xFF0000))
            return

        vc = ctx.voice_client or await voice_channel.connect(cls=wavelink.Player)
        vc.ctx = ctx

        tracks = await wavelink.Playable.search(query)
        if not tracks:
            await ctx.send(embed=discord.Embed(description="❌ No results found.", color=0xFF0000))
            return

        if isinstance(tracks, wavelink.Playlist):
            await vc.queue.put_wait(tracks.tracks)
            await ctx.send(embed=discord.Embed(description=f"✅ Added playlist **{tracks.name}**.", color=0x00FF00))

            if not vc.playing:
                track = await vc.queue.get_wait()
                await vc.play(track)
                await self.display_player_embed(vc, track, ctx)
        else:
            track = tracks[0]

            if not vc.playing:
                await vc.play(track)
                await ctx.send(embed=discord.Embed(description=f"🎵 Now playing **{track.title}**", color=0x00FF00))
                await self.display_player_embed(vc, track, ctx)
            else:
                await vc.queue.put_wait(track)
                await ctx.send(embed=discord.Embed(description=f"✅ Added **{track.title}** to the queue.", color=0x00FF00))

    @commands.command(name="skip", brief="Skip the current song.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def skip(self, ctx):
        vc = ctx.voice_client
        if not vc or not vc.playing:
            await ctx.send(embed=discord.Embed(description="❌ No song playing.", color=0xFF0000))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="❌ You must be in the same voice channel.", color=0xFF0000))
            return

        await vc.stop()
        await ctx.send(embed=discord.Embed(description="⏭️ Skipped the current song.", color=0x00FF00))

    @commands.command(name="stop", brief="Stop music and disconnect.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def stop(self, ctx):
        vc = ctx.voice_client
        if not vc:
            await ctx.send(embed=discord.Embed(description="❌ Not connected to a voice channel.", color=0xFF0000))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="❌ You must be in the same voice channel.", color=0xFF0000))
            return

        vc.queue.clear()
        # Clear voice channel status before disconnecting
        try:
            await vc.channel.edit(status=None)
        except:
            pass  # Ignore errors if channel doesn't exist or no permissions
        await vc.disconnect()
        await ctx.send(embed=discord.Embed(description="⏹️ Stopped and disconnected.", color=0x00FF00))

    @commands.command(name="pause", brief="Pause the current song.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def pause(self, ctx):
        vc = ctx.voice_client
        if not vc or not vc.playing:
            await ctx.send(embed=discord.Embed(description="❌ No song playing.", color=0xFF0000))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="❌ You must be in the same voice channel.", color=0xFF0000))
            return

        if not vc.paused:
            await vc.pause(True)
            # Update voice channel status to show paused
            try:
                await vc.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{vc.current.title}** - {vc.current.author}")
            except:
                pass  # Ignore errors if channel doesn't exist or no permissions
            await ctx.send(embed=discord.Embed(description="⏸️ Paused the current song.", color=0x00FF00))
        else:
            await ctx.send(embed=discord.Embed(description="❌ Already paused.", color=0xFF0000))

    @commands.command(name="resume", brief="Resume the paused song.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def resume(self, ctx):
        vc = ctx.voice_client
        if not vc or not vc.playing:
            await ctx.send(embed=discord.Embed(description="❌ No song playing.", color=0xFF0000))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="❌ You must be in the same voice channel.", color=0xFF0000))
            return

        if vc.paused:
            await vc.pause(False)
            # Update voice channel status to show playing
            try:
                await vc.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{vc.current.title}** - {vc.current.author}")
            except:
                pass  # Ignore errors if channel doesn't exist or no permissions
            await ctx.send(embed=discord.Embed(description="▶️ Resumed the current song.", color=0x00FF00))
        else:
            await ctx.send(embed=discord.Embed(description="❌ Song is not paused.", color=0xFF0000))

    @commands.command(name="queue", aliases=["q"], brief="Show the current queue.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def queue(self, ctx):
        """
        Usage: {0}queue
        Alias: {0}q
        Output: Shows the current music queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="❌ The queue is empty.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        embed = discord.Embed(title="🎵 Music Queue", color=0x1DB954)

        if vc.current:
            embed.add_field(
                name="🎵 Now Playing",
                value=f"**[{vc.current.title}]({vc.current.uri})**\nBy: {vc.current.author}",
                inline=False
            )

        if not vc.queue.is_empty:
            queue_list = []
            for i, track in enumerate(vc.queue[:10], 1):  # Show first 10 tracks
                queue_list.append(f"`{i}.` **[{track.title}]({track.uri})**")

            embed.add_field(
                name=f"📋 Up Next ({len(vc.queue)} tracks)",
                value="\n".join(queue_list),
                inline=False
            )

            if len(vc.queue) > 10:
                embed.add_field(
                    name="➕ More",
                    value=f"And {len(vc.queue) - 10} more tracks...",
                    inline=False
                )

        await ctx.send(embed=embed)

    @commands.command(name="nowplaying", aliases=["np"], brief="Show current song info.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def nowplaying(self, ctx):
        """
        Usage: {0}nowplaying
        Alias: {0}np
        Output: Shows information about the currently playing song.
        """
        vc = ctx.voice_client
        if not vc or not vc.current:
            embed = discord.Embed(description="❌ No song is currently playing.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        track = vc.current
        duration = f"{track.length // 60000}:{(track.length // 1000) % 60:02d}"
        position = f"{vc.position // 60000}:{(vc.position // 1000) % 60:02d}"

        embed = discord.Embed(
            title="🎵 Now Playing",
            description=f"**[{track.title}]({track.uri})**",
            color=0x1DB954
        )
        embed.add_field(name="Artist", value=track.author, inline=True)
        embed.add_field(name="Duration", value=f"{position} / {duration}", inline=True)
        embed.add_field(name="Source", value="YouTube", inline=True)

        if track.artwork:
            embed.set_thumbnail(url=track.artwork)

        embed.set_footer(text=f"Volume: {vc.volume}%")
        await ctx.send(embed=embed)

    @commands.command(name="volume", aliases=["vol"], brief="Set or show volume.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def volume(self, ctx, volume: int = None):
        """
        Usage: {0}volume [1-100]
        Alias: {0}vol
        Output: Sets the volume or shows current volume.
        """
        vc = ctx.voice_client
        if not vc:
            embed = discord.Embed(description="❌ I'm not connected to a voice channel.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="❌ You need to be in the same voice channel as me.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if volume is None:
            embed = discord.Embed(description=f"🔊 Current volume: **{vc.volume}%**", color=0x1DB954)
            await ctx.send(embed=embed)
            return

        if not 1 <= volume <= 100:
            embed = discord.Embed(description="❌ Volume must be between 1 and 100.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        await vc.set_volume(volume)
        embed = discord.Embed(description=f"🔊 Volume set to **{volume}%**", color=0x00FF00)
        await ctx.send(embed=embed)

    @commands.command(name="shuffle", brief="Shuffle the queue.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def shuffle(self, ctx):
        """
        Usage: {0}shuffle
        Output: Shuffles the current queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="❌ The queue is empty.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="❌ You need to be in the same voice channel as me.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        random.shuffle(vc.queue)
        embed = discord.Embed(description=f"🔀 Shuffled **{len(vc.queue)}** tracks in the queue.", color=0x00FF00)
        await ctx.send(embed=embed)

    @commands.command(name="clear", brief="Clear the queue.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def clear(self, ctx):
        """
        Usage: {0}clear
        Output: Clears all tracks from the queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="❌ The queue is already empty.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="❌ You need to be in the same voice channel as me.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        queue_size = len(vc.queue)
        vc.queue.clear()
        embed = discord.Embed(description=f"🗑️ Cleared **{queue_size}** tracks from the queue.", color=0x00FF00)
        await ctx.send(embed=embed)

    @commands.command(name="remove", brief="Remove a track from queue.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def remove(self, ctx, index: int):
        """
        Usage: {0}remove <position>
        Output: Removes a track at the specified position from the queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="❌ The queue is empty.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="❌ You need to be in the same voice channel as me.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not 1 <= index <= len(vc.queue):
            embed = discord.Embed(description=f"❌ Invalid position. Queue has **{len(vc.queue)}** tracks.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        removed_track = vc.queue[index - 1]
        del vc.queue[index - 1]
        embed = discord.Embed(description=f"🗑️ Removed **{removed_track.title}** from position **{index}**.", color=0x00FF00)
        await ctx.send(embed=embed)

    @commands.command(name="move", brief="Move a track in the queue.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def move(self, ctx, from_pos: int, to_pos: int):
        """
        Usage: {0}move <from> <to>
        Output: Moves a track from one position to another in the queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="❌ The queue is empty.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="❌ You need to be in the same voice channel as me.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        queue_len = len(vc.queue)
        if not (1 <= from_pos <= queue_len) or not (1 <= to_pos <= queue_len):
            embed = discord.Embed(description=f"❌ Invalid positions. Queue has **{queue_len}** tracks.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        track = vc.queue[from_pos - 1]
        del vc.queue[from_pos - 1]
        vc.queue.put_at_index(to_pos - 1, track)

        embed = discord.Embed(description=f"📋 Moved **{track.title}** from position **{from_pos}** to **{to_pos}**.", color=0x00FF00)
        await ctx.send(embed=embed)

    @commands.command(name="seek", brief="Seek to a position in the current track.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def seek(self, ctx, *, time_str: str):
        """
        Usage: {0}seek <time>
        Examples: {0}seek 1:30, {0}seek 90
        Output: Seeks to the specified time in the current track.
        """
        vc = ctx.voice_client
        if not vc or not vc.current:
            embed = discord.Embed(description="❌ No song is currently playing.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="❌ You need to be in the same voice channel as me.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        # Parse time string (supports formats like "1:30" or "90")
        try:
            if ":" in time_str:
                parts = time_str.split(":")
                if len(parts) == 2:
                    minutes, seconds = int(parts[0]), int(parts[1])
                    seek_time = (minutes * 60 + seconds) * 1000
                else:
                    raise ValueError("Invalid time format")
            else:
                seek_time = int(time_str) * 1000
        except ValueError:
            embed = discord.Embed(description="❌ Invalid time format. Use `MM:SS` or seconds.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if seek_time < 0 or seek_time > vc.current.length:
            embed = discord.Embed(description="❌ Seek time is out of range.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        await vc.seek(seek_time)
        seek_display = f"{seek_time // 60000}:{(seek_time // 1000) % 60:02d}"
        embed = discord.Embed(description=f"⏩ Seeked to **{seek_display}**", color=0x00FF00)
        await ctx.send(embed=embed)

    @commands.command(name="loop", brief="Toggle loop mode.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def loop(self, ctx, mode: str = None):
        """
        Usage: {0}loop [off/track/queue]
        Output: Toggles loop mode or sets specific loop mode.
        """
        vc = ctx.voice_client
        if not vc:
            embed = discord.Embed(description="❌ I'm not connected to a voice channel.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="❌ You need to be in the same voice channel as me.", color=0xFF0000)
            await ctx.send(embed=embed)
            return

        if mode is None:
            # Toggle between normal and loop
            if vc.queue.mode == wavelink.QueueMode.normal:
                vc.queue.mode = wavelink.QueueMode.loop
                embed = discord.Embed(description="🔁 Loop mode **enabled** (current track).", color=0x00FF00)
            else:
                vc.queue.mode = wavelink.QueueMode.normal
                embed = discord.Embed(description="🔁 Loop mode **disabled**.", color=0x00FF00)
        else:
            mode = mode.lower()
            if mode in ["off", "disable", "normal"]:
                vc.queue.mode = wavelink.QueueMode.normal
                embed = discord.Embed(description="🔁 Loop mode **disabled**.", color=0x00FF00)
            elif mode in ["track", "song", "current"]:
                vc.queue.mode = wavelink.QueueMode.loop
                embed = discord.Embed(description="🔁 Loop mode **enabled** (current track).", color=0x00FF00)
            elif mode in ["queue", "all"]:
                vc.queue.mode = wavelink.QueueMode.loop_all
                embed = discord.Embed(description="🔁 Loop mode **enabled** (entire queue).", color=0x00FF00)
            else:
                embed = discord.Embed(description="❌ Invalid loop mode. Use `off`, `track`, or `queue`.", color=0xFF0000)

        await ctx.send(embed=embed)


async def setup(bot):
    await bot.add_cog(Music(bot))
