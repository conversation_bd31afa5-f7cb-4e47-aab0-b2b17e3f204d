from urllib.parse import urlencode

import re
import base64
import time
import json
import logging


from config import SPOTIFY
from core import bot as client

logger = logging.getLogger(__name__)


def url_to_uri(url):
    if "open.spotify.com" in url:
        sub_regex = r"(http[s]?:\/\/)?(open.spotify.com)\/"
        url = "spotify:" + re.sub(sub_regex, "", url)
        url = url.replace("/", ":")
        # remove session id (and other query stuff)
        uri = re.sub("\?.*", "", url)
        return uri


class CONSTANTS:
    WHITE_ICON = "https://cdn.discordapp.com/attachments/872338764276576266/927649624888602624/spotify_white.png"
    API_URL = "https://api.spotify.com/v1/"
    AUTH_URL = "https://accounts.spotify.com/authorize"
    TOKEN_URL = "https://accounts.spotify.com/api/token"
    SCOPES = [
        # Spotify connect
        "user-read-playback-state",
        "user-modify-playback-state",
        "user-read-currently-playing",
        # Users
        "user-read-private",
        # Follow
        "user-follow-modify",
        "user-follow-read",
        # Library
        "user-library-modify",
        "user-library-read",
        # Listening history
        "user-read-playback-position",
        "user-top-read",
        "user-read-recently-played",
        # Playlists
        "playlist-modify-private",
        "playlist-read-collaborative",
        "playlist-read-private",
        "playlist-modify-public",
    ]


class Oauth:
    def __init__(self, scope=None):
        self.scope = " ".join(CONSTANTS.SCOPES)

        self.client_token = None

    @property
    def headers(self):
        """
        Return proper headers for all token requests
        """
        auth_header = base64.b64encode(
            (SPOTIFY.client_id + ":" + SPOTIFY.client_secret).encode("ascii")
        )
        return {
            "Authorization": "Basic %s" % auth_header.decode("ascii"),
            "Content-Type": "application/x-www-form-urlencoded",
        }

    def get_auth_url(self, state):
        """
        Return an authorization url to get an access code
        """
        params = {
            "client_id": SPOTIFY.client_id,
            "response_type": "code",
            "redirect_uri": SPOTIFY.redirect_uri,
            "state": state,  # This was already here but make sure it's being passed
            "scope": " ".join(CONSTANTS.SCOPES),
            # "show_dialog": True
        }
        constructed = urlencode(params)
        return "%s?%s" % (CONSTANTS.AUTH_URL, constructed)

    def validate_token(self, token_info):
        """Checks a token is valid"""
        now = int(time.time())
        return token_info["expires_at"] - now > 60

    async def get_access_token(self, user_id, token_info):
        """Gets the token or creates a new one if expired"""
        try:
            # Ensure expires_at is set if not present
            if "expires_at" not in token_info:
                token_info["expires_at"] = int(time.time()) + token_info.get("expires_in", 3600)

            if self.validate_token(token_info):
                logger.debug(f"Using existing valid token for user {user_id}")
                return token_info["access_token"]

            logger.debug(f"Token expired for user {user_id}, refreshing...")
            token_info = await self.refresh_access_token(
                user_id, token_info.get("refresh_token")
            )

            return token_info["access_token"]
        except Exception as e:
            logger.error(f"Error getting access token for user {user_id}: {e}")
            raise

    async def refresh_access_token(self, user_id, refresh_token):
        try:
            if not refresh_token:
                raise ValueError("No refresh token provided")

            params = {"grant_type": "refresh_token", "refresh_token": refresh_token}
            logger.debug(f"Refreshing token for user {user_id}")

            token_info = await client.post(
                CONSTANTS.TOKEN_URL, data=params, headers=self.headers, res_method="json"
            )

            if "error" in token_info:
                logger.error(f"Spotify API error refreshing token for user {user_id}: {token_info}")
                raise Exception(f"Spotify API error: {token_info.get('error_description', token_info.get('error'))}")

            if not token_info.get("refresh_token"):
                # Didn't get new refresh token.
                # Old one is still valid.
                token_info["refresh_token"] = refresh_token

            # Set expires_at
            token_info["expires_at"] = int(time.time()) + token_info.get("expires_in", 3600)

            query = """
                    INSERT INTO spotify_auth
                    VALUES ($1, $2)
                    ON CONFLICT (user_id)
                    DO UPDATE SET token_info = $2
                    WHERE spotify_auth.user_id = $1;
                    """
            await client.cxn.execute(query, user_id, json.dumps(token_info))
            logger.debug(f"Successfully refreshed and stored token for user {user_id}")

            return token_info
        except Exception as e:
            logger.error(f"Error refreshing access token for user {user_id}: {e}")
            raise

    async def request_access_token(self, code):
        try:
            payload = {
                "grant_type": "authorization_code",
                "code": code,
                "redirect_uri": SPOTIFY.redirect_uri,
            }
            logger.debug(f"Requesting access token with code: {code[:10]}...")

            token_info = await client.post(
                CONSTANTS.TOKEN_URL, data=payload, headers=self.headers, res_method="json"
            )

            if "error" in token_info:
                logger.error(f"Spotify API error requesting token: {token_info}")
                raise Exception(f"Spotify API error: {token_info.get('error_description', token_info.get('error'))}")

            # Set expires_at
            token_info["expires_at"] = int(time.time()) + token_info.get("expires_in", 3600)
            logger.debug("Successfully obtained access token")

            return token_info
        except Exception as e:
            logger.error(f"Error requesting access token: {e}")
            raise

    async def get_client_token(self):
        """Gets the token or creates a new one if expired"""
        if self.client_token and self.validate_token(self.client_token):
            return self.client_token["access_token"]

        client_token = await self.request_client_token()

        client_token["expires_at"] = int(time.time()) + client_token["expires_in"]
        self.client_token = client_token
        return self.client_token["access_token"]

    async def request_client_token(self):
        """Obtains a token from Spotify and returns it"""
        payload = {"grant_type": "client_credentials"}
        return await client.post(
            CONSTANTS.TOKEN_URL, data=payload, headers=self.headers, res_method="json"
        )


oauth = Oauth()


class User:  # Spotify user w discord user_id
    def __init__(self, user_id, token_info):
        self.user_id = user_id
        self.token_info = token_info

    @classmethod
    async def load(cls, user_id):
        query = """
                SELECT token_info
                FROM spotify_auth
                WHERE user_id = $1;
                """
        token_info = await client.cxn.fetchval(query, int(user_id))

        if token_info:
            token_info = json.loads(token_info)
            return cls(user_id, token_info)

    async def auth(self):
        access_token = await oauth.get_access_token(self.user_id, self.token_info)

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        return headers

    async def get(self, url):
        return await client.get(url, headers=await self.auth(), res_method="json")

    async def put(self, url, headers=None, json=None, res_method=None):
        headers = headers or await self.auth()
        return await client.put(url, headers=headers, json=json, res_method=res_method)

    async def get_profile(self):
        return await self.get(CONSTANTS.API_URL + "me")

    async def get_playback_state(self):
        return await self.get(CONSTANTS.API_URL + "me/player")

    async def get_currently_playing(self):
        return await self.get(CONSTANTS.API_URL + "me/player/currently-playing")

    async def get_devices(self):
        return await self.get(CONSTANTS.API_URL + "me/player/devices")

    async def transfer_playback(self, devices, play: bool = False):
        return await self.put(
            CONSTANTS.API_URL + "me/player", json={"device_ids": devices, "play": play}
        )

    async def get_recently_played(self, limit=50):
        params = {"limit": limit}
        query_params = urlencode(params)
        return await self.get(
            CONSTANTS.API_URL + "me/player/recently-played?" + query_params
        )

    async def get_top_tracks(self, limit=50, time_range="long_term"):
        params = {"limit": limit, "time_range": time_range}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/top/tracks?" + query_params)

    async def get_top_artists(self, limit=50, time_range="long_term"):
        params = {"limit": limit, "time_range": time_range}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/top/artists?" + query_params)

    async def get_top_albums(self, limit=50):
        params = {"limit": limit}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/albums?" + query_params)

    async def pause(self):
        return await self.put(CONSTANTS.API_URL + "me/player/pause")

    async def play(self, **kwargs):
        return await self.put(CONSTANTS.API_URL + "me/player/play", json=kwargs)

    async def skip_to_next(self):
        return await client.post(
            CONSTANTS.API_URL + "me/player/next",
            headers=await self.auth(),
            res_method=None,
        )

    async def skip_to_previous(self):
        return await client.post(
            CONSTANTS.API_URL + "me/player/previous",
            headers=await self.auth(),
            res_method=None,
        )

    async def seek(self, position):
        params = {"position_ms": position * 1000}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/seek?" + query_params)

    async def repeat(self, option):
        params = {"state": option}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/repeat?" + query_params)

    async def shuffle(self, option: bool):
        params = {"state": option}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/shuffle?" + query_params)

    async def volume(self, amount):
        params = {"volume_percent": amount}
        query_params = urlencode(params)
        return await self.put(CONSTANTS.API_URL + "me/player/volume?" + query_params)

    async def enqueue(self, uri):
        params = {"uri": uri}
        query_params = urlencode(params)
        return await client.post(
            CONSTANTS.API_URL + "me/player/queue?" + query_params,
            headers=await self.auth(),
            res_method=None,
        )

    async def get_playlists(self, limit=50, offset=0):
        """Get a user's owned and followed playlists"""
        params = {"limit": limit, "offset": offset}
        query_params = urlencode(params)
        return await self.get(CONSTANTS.API_URL + "me/playlists?" + query_params)


async def auth():
    access_token = await oauth.get_client_token()

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    return headers


async def _get(url):
    return await client.get(url, headers=await auth(), res_method="json")


async def get_playlist(uri):
    playlist_id = uri.split(":")[-1]
    return await _get(CONSTANTS.API_URL + f"playlists/{playlist_id}")


async def get_user_playlists(username, limit=50, offset=0):
    """Get a user's owned and followed playlists"""

    params = {"limit": limit, "offset": offset}
    query_params = urlencode(params)
    return await _get(CONSTANTS.API_URL + f"users/{username}/playlists?" + query_params)

