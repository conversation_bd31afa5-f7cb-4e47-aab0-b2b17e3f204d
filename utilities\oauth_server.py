import asyncio
from aiohttp import web
import json
from utilities.spotify import oauth
from core import bot

async def handle_spotify_callback(request):
    """Handle the Spotify OAuth callback"""
    code = request.query.get('code')
    state = request.query.get('state')  # Discord user ID
    
    if not code:
        return web.Response(text="No authorization code provided")
    
    if not state:
        return web.Response(text="No state parameter provided")
    
    try:
        # Exchange code for token
        token_info = await oauth.request_access_token(code)
        
        # Store token info in database with Discord user ID
        user_id = int(state)
        query = """
                INSERT INTO spotify_auth
                VALUES ($1, $2)
                ON CONFLICT (user_id)
                DO UPDATE SET token_info = $2
                WHERE spotify_auth.user_id = $1;
                """
        await bot.cxn.execute(query, user_id, json.dumps(token_info))
        
        # Success page
        html = """
        <html>
        <head><title>Spotify Connected!</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: #1db954;">🎵 Spotify Connected Successfully!</h1>
            <p>Your Spotify account has been linked to your Discord account.</p>
            <p>You can now close this window and return to Discord to use Spotify commands.</p>
        </body>
        </html>
        """
        return web.Response(text=html, content_type='text/html')
        
    except Exception as e:
        error_html = f"""
        <html>
        <head><title>Connection Failed</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: #e22134;">❌ Connection Failed</h1>
            <p>Error: {str(e)}</p>
            <p>Please try again or contact support.</p>
        </body>
        </html>
        """
        return web.Response(text=error_html, content_type='text/html')

async def start_oauth_server():
    """Start the OAuth callback server"""
    app = web.Application()
    app.add_routes([web.get('/callback', handle_spotify_callback)])
    
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '127.0.0.1', 8080)
    await site.start()
    
    return runner
